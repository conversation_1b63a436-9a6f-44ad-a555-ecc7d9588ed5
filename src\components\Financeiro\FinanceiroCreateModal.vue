<template>
  <div class="modal fade" id="modalFinanceiroCreate" tabindex="-1" aria-labelledby="modalFinanceiroCreateLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalFinanceiroCreateLabel">
            {{ modalTitle }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Seletor de Tipo -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="nav-wrapper position-relative end-0">
                <ul class="nav nav-pills nav-fill p-1" role="tablist">
                  <li class="nav-item">
                    <a class="nav-link mb-0 px-0 py-1" 
                       :class="{ active: activeType === 'fatura' }"
                       @click="activeType = 'fatura'"
                       data-bs-toggle="tab" 
                       href="#fatura" 
                       role="tab">
                      <i class="fas fa-file-invoice me-2"></i>
                      Criar Fatura Direta
                    </a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link mb-0 px-0 py-1" 
                       :class="{ active: activeType === 'orcamento' }"
                       @click="activeType = 'orcamento'"
                       data-bs-toggle="tab" 
                       href="#orcamento" 
                       role="tab">
                      <i class="fas fa-calculator me-2"></i>
                      Criar Orçamento
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Conteúdo das abas -->
          <div class="tab-content" id="tabs-tabContent">
            <!-- Aba Fatura Direta -->
            <div class="tab-pane fade" 
                 :class="{ 'show active': activeType === 'fatura' }" 
                 id="fatura" 
                 role="tabpanel">
              <fatura-form
                :form="faturaForm"
                :errors="errors"
                :pacientes="pacientes"
                :dentistas="dentistas"
                :preselected-paciente="preselectedPaciente"
                :paciente-selecionado="pacienteSelecionado"
                @update-form="updateFaturaForm"
                @abrir-busca-paciente="abrirBuscaPaciente"
                @limpar-paciente="limparPaciente"
              />
            </div>

            <!-- Aba Orçamento -->
            <div class="tab-pane fade" 
                 :class="{ 'show active': activeType === 'orcamento' }" 
                 id="orcamento" 
                 role="tabpanel">
              <orcamento-form
                :form="orcamentoForm"
                :errors="errors"
                :pacientes="pacientes"
                :dentistas="dentistas"
                :servicos-produtos="servicosProdutos"
                :preselected-paciente="preselectedPaciente"
                :paciente-selecionado="pacienteSelecionado"
                @update-form="updateOrcamentoForm"
                @add-item="addOrcamentoItem"
                @remove-item="removeOrcamentoItem"
                @search-servicos="searchServicos"
                @abrir-busca-paciente="abrirBuscaPaciente"
                @limpar-paciente="limparPaciente"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" @click="save" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ saving ? 'Salvando...' : getSaveButtonText() }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Busca de Paciente -->
  <paciente-busca-modal
    ref="pacienteBuscaModal"
    @paciente-selecionado="onPacienteSelecionado"
  />
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import { orcamentoService } from '@/services/orcamentoService';
import { servicoProdutoService } from '@/services/servicoProdutoService';
import { openModal, closeModal } from '@/utils/modalHelper';
import { searchPacientes } from '@/services/pacientesService';
import { getDentistas } from '@/services/dentistasService';
import cSwal from '@/utils/cSwal.js';
import FaturaForm from './FaturaForm.vue';
import OrcamentoForm from './OrcamentoForm.vue';
import PacienteBuscaModal from '@/components/Global/PacienteBuscaModal.vue';

export default {
  name: 'FinanceiroCreateModal',
  components: {
    FaturaForm,
    OrcamentoForm,
    PacienteBuscaModal,
  },
  data() {
    return {
      activeType: 'fatura',
      saving: false,
      preselectedPaciente: null,
      pacienteSelecionado: null,
      pacientes: [],
      dentistas: [],
      servicosProdutos: [],
      faturaForm: {
        paciente_id: '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      },
      orcamentoForm: {
        paciente_id: '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: [this.createEmptyItem()]
      },
      errors: {}
    };
  },
  computed: {
    modalTitle() {
      return this.activeType === 'fatura' ? 'Nova Fatura' : 'Novo Orçamento';
    }
  },
  methods: {
    open(type = 'fatura', pacienteId = null, pacienteObj = null) {
      this.activeType = type;
      this.preselectedPaciente = pacienteId;
      this.pacienteSelecionado = pacienteObj;
      this.resetForms();
      this.clearErrors();

      if (pacienteId) {
        this.faturaForm.paciente_id = pacienteId;
        this.orcamentoForm.paciente_id = pacienteId;
      }

      openModal('modalFinanceiroCreate');
    },

    resetForms() {
      this.faturaForm = {
        paciente_id: this.preselectedPaciente || '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      };

      this.orcamentoForm = {
        paciente_id: this.preselectedPaciente || '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: [this.createEmptyItem()]
      };
    },

    clearErrors() {
      this.errors = {};
    },

    updateFaturaForm(field, value) {
      this.faturaForm[field] = value;
    },

    updateOrcamentoForm(field, value) {
      this.orcamentoForm[field] = value;
    },

    createEmptyItem() {
      return {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: ''
      };
    },

    addOrcamentoItem() {
      this.orcamentoForm.itens.push(this.createEmptyItem());
    },

    removeOrcamentoItem(index) {
      if (this.orcamentoForm.itens.length > 1) {
        this.orcamentoForm.itens.splice(index, 1);
      }
    },

    async searchServicos(termo) {
      try {
        const response = await servicoProdutoService.buscarParaOrcamento({ busca: termo });
        this.servicosProdutos = response.data.data || [];
      } catch (error) {
        console.error('Erro ao buscar serviços:', error);
      }
    },

    getSaveButtonText() {
      return this.activeType === 'fatura' ? 'Criar Fatura' : 'Criar Orçamento';
    },

    async save() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        if (this.activeType === 'fatura') {
          await this.saveFatura();
        } else {
          await this.saveOrcamento();
        }

        this.closeModal();
        this.$emit('saved');
      } catch (error) {
        console.error('Erro ao salvar:', error);
        
        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar');
        }
      } finally {
        this.saving = false;
      }
    },

    async saveFatura() {
      await financeiroService.createFatura(this.faturaForm);
      cSwal.cSuccess('Fatura criada com sucesso');
    },

    async saveOrcamento() {
      await orcamentoService.createOrcamento(this.orcamentoForm);
      cSwal.cSuccess('Orçamento criado com sucesso');
    },

    validateForm() {
      if (this.activeType === 'fatura') {
        const validation = financeiroService.validateFaturaData(this.faturaForm);
        this.errors = validation.errors;
        return validation.isValid;
      } else {
        const validation = orcamentoService.validateOrcamentoData(this.orcamentoForm);
        this.errors = validation.errors;
        return validation.isValid;
      }
    },

    closeModal() {
      closeModal('modalFinanceiroCreate');
    },

    abrirBuscaPaciente() {
      this.$refs.pacienteBuscaModal.open();
    },

    onPacienteSelecionado(paciente) {
      this.pacienteSelecionado = paciente;
      this.faturaForm.paciente_id = paciente.id;
      this.orcamentoForm.paciente_id = paciente.id;
    },

    limparPaciente() {
      this.pacienteSelecionado = null;
      this.faturaForm.paciente_id = '';
      this.orcamentoForm.paciente_id = '';
    },

    async loadPacientes() {
      try {
        const response = await searchPacientes();
        this.pacientes = response || [];
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
        this.pacientes = [];
      }
    },

    async loadDentistas() {
      try {
        const response = await getDentistas();
        this.dentistas = response || [];
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
        this.dentistas = [];
      }
    },

    async loadServicosProdutos() {
      try {
        const response = await servicoProdutoService.getServicosProdutos({ ativo: 1 });
        this.servicosProdutos = response.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar serviços/produtos:', error);
        this.servicosProdutos = [];
      }
    }
  },

  mounted() {
    this.loadPacientes();
    this.loadDentistas();
    this.loadServicosProdutos();
  }
};
</script>

<style scoped>
.modal-content {
  border-radius: 15px;
}

.nav-pills .nav-link.active {
  background-color: #5e72e4;
}

.form-control:focus,
.form-select:focus {
  border-color: #5e72e4;
  box-shadow: 0 0 0 0.2rem rgba(94, 114, 228, 0.25);
}

.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}
</style>
