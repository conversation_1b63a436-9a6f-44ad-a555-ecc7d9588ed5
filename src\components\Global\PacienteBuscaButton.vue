<template>
  <div class="paciente-busca-wrapper">
    <!-- Campo de exibição do paciente selecionado -->
    <div class="input-group">
      <input 
        type="text" 
        class="form-control" 
        :value="displayValue"
        :placeholder="placeholder"
        :class="{ 'is-invalid': hasError }"
        readonly>
      <button 
        type="button" 
        class="btn btn-outline-primary" 
        @click="abrirModal"
        :disabled="disabled">
        <i class="fas fa-search"></i>
      </button>
      <button 
        v-if="pacienteSelecionado && !disabled" 
        type="button" 
        class="btn btn-outline-danger" 
        @click="limparSelecao"
        title="Limpar seleção">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <!-- Feedback de erro -->
    <div v-if="hasError && errorMessage" class="invalid-feedback d-block">
      {{ errorMessage }}
    </div>

    <!-- Informações adicionais do paciente -->
    <div v-if="pacienteSelecionado && showDetails" class="mt-2">
      <div class="d-flex flex-wrap gap-1">
        <small class="badge bg-light text-dark">
          <i class="fas fa-hashtag me-1"></i>
          ID: {{ String(pacienteSelecionado.id_ficha).padStart(3, '0') }}
        </small>
        <small v-if="pacienteSelecionado.telefone" class="badge bg-light text-dark">
          <i class="fas fa-phone me-1"></i>
          {{ formatPhone(pacienteSelecionado.telefone) }}
        </small>
        <small v-if="pacienteSelecionado.email" class="badge bg-light text-dark">
          <i class="fas fa-envelope me-1"></i>
          {{ pacienteSelecionado.email }}
        </small>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PacienteBuscaButton',
  props: {
    // Paciente atualmente selecionado
    pacienteSelecionado: {
      type: Object,
      default: null
    },
    // Placeholder do campo
    placeholder: {
      type: String,
      default: 'Clique na lupa para buscar um paciente...'
    },
    // Se o componente está desabilitado
    disabled: {
      type: Boolean,
      default: false
    },
    // Se há erro de validação
    hasError: {
      type: Boolean,
      default: false
    },
    // Mensagem de erro
    errorMessage: {
      type: String,
      default: ''
    },
    // Se deve mostrar detalhes do paciente
    showDetails: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    displayValue() {
      if (this.pacienteSelecionado) {
        return this.pacienteSelecionado.nome;
      }
      return '';
    }
  },
  methods: {
    abrirModal() {
      this.$emit('abrir-busca');
    },

    limparSelecao() {
      this.$emit('limpar-selecao');
    },

    formatPhone(phone) {
      if (!phone) return '';
      
      // Remove tudo que não é número
      const numbers = phone.replace(/\D/g, '');
      
      // Formata como (XX) XXXXX-XXXX ou (XX) XXXX-XXXX
      if (numbers.length === 11) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
      } else if (numbers.length === 10) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      }
      
      return phone;
    }
  }
};
</script>

<style scoped>
.paciente-busca-wrapper {
  width: 100%;
}

.input-group .btn {
  border-left: 0;
}

.input-group .form-control:focus {
  border-color: #5e72e4;
  box-shadow: none;
}

.input-group .form-control:focus + .btn {
  border-color: #5e72e4;
}

.badge {
  font-size: 0.75rem;
}

.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}
</style>
