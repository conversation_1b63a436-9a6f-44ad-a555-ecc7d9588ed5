<template>
  <div class="financeiro-analitico">
    <!-- Filtros e Busca -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-0">Filtros de Busca</h6>
              <button class="btn btn-sm btn-outline-secondary" @click="clearFilters">
                <i class="fas fa-times me-1"></i>
                Limpar
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Paciente</label>
                <select class="form-select" v-model="filters.paciente_id">
                  <option value="">Todos os pacientes</option>
                  <option v-for="paciente in pacientes" :key="paciente.id" :value="paciente.id">
                    {{ paciente.nome }}
                  </option>
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Status</label>
                <select class="form-select" v-model="filters.status">
                  <option value="">Todos</option>
                  <option value="pendente">Pendente</option>
                  <option value="pago">Pago</option>
                  <option value="vencido">Vencido</option>
                  <option value="cancelado">Cancelado</option>
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Data Início</label>
                <input type="date" class="form-control" v-model="filters.data_inicio">
              </div>
              <div class="col-md-2">
                <label class="form-label">Data Fim</label>
                <input type="date" class="form-control" v-model="filters.data_fim">
              </div>
              <div class="col-md-2">
                <label class="form-label">Apenas Vencidas</label>
                <div class="form-check form-switch mt-2">
                  <input class="form-check-input" type="checkbox" v-model="filters.vencidas">
                  <label class="form-check-label">Vencidas</label>
                </div>
              </div>
              <div class="col-md-1 d-flex align-items-end">
                <button class="btn btn-primary w-100" @click="applyFilters">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas Rápidas -->
    <div class="row mb-4" v-if="estatisticas">
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <h5 class="text-success">{{ formatCurrency(estatisticas.total_pago) }}</h5>
            <p class="mb-0 text-sm">Recebido ({{ estatisticas.quantidade_paga || 0 }})</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <h5 class="text-warning">{{ formatCurrency(estatisticas.total_pendente) }}</h5>
            <p class="mb-0 text-sm">Pendente ({{ estatisticas.quantidade_pendente || 0 }})</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <h5 class="text-danger">{{ formatCurrency(estatisticas.total_vencido) }}</h5>
            <p class="mb-0 text-sm">Vencido ({{ estatisticas.quantidade_vencida || 0 }})</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <h5 class="text-info">{{ formatCurrency(estatisticas.total_geral) }}</h5>
            <p class="mb-0 text-sm">Total Geral</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabela de Faturas -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-0">Faturas</h6>
              <button class="btn btn-primary btn-sm" @click="$emit('create')">
                <i class="fas fa-plus me-1"></i>
                Nova Fatura
              </button>
            </div>
          </div>
          <div class="card-body px-0 pt-0 pb-2">
            <div class="table-responsive p-0">
              <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Paciente
                    </th>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="faturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      Nenhuma fatura encontrada
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in faturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.paciente?.nome || 'Paciente não informado' }}</h6>
                          <p class="text-xs text-secondary mb-0">
                            ID: {{ fatura.paciente?.id || '-' }}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p class="text-xs font-weight-bold mb-0">{{ fatura.descricao }}</p>
                      <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                        {{ fatura.observacoes }}
                      </p>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.parcelas_total > 1" class="text-xs text-secondary">
                        {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <div class="dropdown">
                        <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('edit', fatura)">
                              <i class="fas fa-edit me-2"></i>
                              Editar
                            </a>
                          </li>
                          <li v-if="fatura.status === 'pendente'">
                            <a class="dropdown-item" href="#" @click.prevent="markAsPaid(fatura)">
                              <i class="fas fa-check me-2"></i>
                              Marcar como Pago
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('delete', fatura.id)">
                              <i class="fas fa-trash me-2"></i>
                              Cancelar
                            </a>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'FinanceiroAnalitico',
  props: {
    faturas: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    estatisticas: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      pacientes: [],
      filters: {
        paciente_id: '',
        status: '',
        data_inicio: '',
        data_fim: '',
        vencidas: false
      }
    };
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    applyFilters() {
      this.$emit('refresh', this.filters);
    },

    clearFilters() {
      this.filters = {
        paciente_id: '',
        status: '',
        data_inicio: '',
        data_fim: '',
        vencidas: false
      };
      this.applyFilters();
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    async loadPacientes() {
      try {
        // TODO: Implementar carregamento de pacientes
        // const response = await pacientesService.getPacientes();
        // this.pacientes = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
      }
    }
  },

  mounted() {
    this.loadPacientes();
  }
};
</script>

<style scoped>
.financeiro-analitico .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.badge-warning {
  background-color: #fb6340;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}
</style>
